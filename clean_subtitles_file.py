#!/usr/bin/env python3
"""
Скрипт для очистки файла субтитров от повторений и создания чистого текста
"""
import re
import os

def clean_subtitles_text(text):
    """Очищает текст субтитров от повторений и лишних символов"""
    
    # Удаляем HTML теги
    text = re.sub(r'&gt;', '>', text)
    text = re.sub(r'&lt;', '<', text)
    text = re.sub(r'<[^>]+>', '', text)
    
    # Разбиваем на предложения
    sentences = re.split(r'[.!?]+', text)
    
    clean_sentences = []
    prev_sentence = ""
    
    for sentence in sentences:
        sentence = sentence.strip()
        
        # Пропускаем пустые предложения
        if not sentence or len(sentence) < 5:
            continue
        
        # Проверяем на повторения
        # Если предложение очень похоже на предыдущее, пропускаем
        if prev_sentence and are_sentences_similar(sentence, prev_sentence):
            continue
        
        clean_sentences.append(sentence)
        prev_sentence = sentence
    
    # Дополнительная очистка от тройных повторений
    final_sentences = remove_triple_repetitions(clean_sentences)
    
    return final_sentences

def are_sentences_similar(sent1, sent2, threshold=0.8):
    """Проверяет, похожи ли два предложения"""
    # Простая проверка на основе общих слов
    words1 = set(sent1.lower().split())
    words2 = set(sent2.lower().split())
    
    if not words1 or not words2:
        return False
    
    intersection = len(words1.intersection(words2))
    union = len(words1.union(words2))
    
    similarity = intersection / union if union > 0 else 0
    return similarity > threshold

def remove_triple_repetitions(sentences):
    """Удаляет тройные повторения предложений"""
    if len(sentences) < 3:
        return sentences
    
    clean_sentences = []
    i = 0
    
    while i < len(sentences):
        current = sentences[i]
        
        # Проверяем, есть ли тройное повторение
        if (i + 2 < len(sentences) and 
            are_sentences_similar(current, sentences[i + 1]) and 
            are_sentences_similar(current, sentences[i + 2])):
            
            # Добавляем только одну копию
            clean_sentences.append(current)
            i += 3  # Пропускаем следующие две копии
        else:
            clean_sentences.append(current)
            i += 1
    
    return clean_sentences

def clean_subtitles_file(input_file, output_file):
    """Очищает файл субтитров и сохраняет результат"""
    
    print(f"Читаем файл: {input_file}")
    
    # Читаем исходный файл
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Размер исходного файла: {len(content)} символов")
    
    # Очищаем текст
    print("Очищаем текст от повторений...")
    clean_sentences = clean_subtitles_text(content)
    
    print(f"Получено {len(clean_sentences)} уникальных предложений")
    
    # Формируем финальный текст
    final_text = '. '.join(clean_sentences)
    if final_text and not final_text.endswith('.'):
        final_text += '.'
    
    # Разбиваем на абзацы для лучшей читаемости
    paragraphs = []
    current_paragraph = []
    
    for i, sentence in enumerate(clean_sentences):
        current_paragraph.append(sentence)
        
        # Создаем новый абзац каждые 5-7 предложений
        if len(current_paragraph) >= 5:
            paragraphs.append('. '.join(current_paragraph) + '.')
            current_paragraph = []
    
    # Добавляем оставшиеся предложения
    if current_paragraph:
        paragraphs.append('. '.join(current_paragraph) + '.')
    
    formatted_text = '\n\n'.join(paragraphs)
    
    # Сохраняем результат
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(formatted_text)
    
    print(f"\n✓ Очистка завершена!")
    print(f"Результат сохранен в: {output_file}")
    print(f"Размер очищенного текста: {len(formatted_text)} символов")
    print(f"Количество абзацев: {len(paragraphs)}")
    
    # Показываем начало результата
    print(f"\nНачало очищенного текста:")
    print("-" * 60)
    print(formatted_text[:500] + "...")
    print("-" * 60)
    
    return True

if __name__ == "__main__":
    input_file = "subtitles_0Uu_VJeVVfo.en.txt"
    output_file = "subtitles_0Uu_VJeVVfo_clean.en.txt"
    
    if not os.path.exists(input_file):
        print(f"Ошибка: файл {input_file} не найден!")
        exit(1)
    
    print("Очистка файла субтитров от повторений")
    print("=" * 50)
    
    try:
        clean_subtitles_file(input_file, output_file)
        print(f"\nТеперь вы можете перевести очищенный файл: {output_file}")
    except Exception as e:
        print(f"Ошибка: {e}")
