#!/usr/bin/env python3
import re
import os
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.formatters import TextFormatter
import yt_dlp

def extract_video_id(url):
    """Извлекает video_id из YouTube URL"""
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([^&\n?#]+)',
        r'youtube\.com/watch\?.*v=([^&\n?#]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None

def get_transcript_via_api(video_id, languages=['ru', 'en']):
    """Получает расшифровку через YouTube Transcript API"""
    try:
        # Пробуем получить расшифровку на нужных языках
        for lang in languages:
            try:
                print(f"  Пробуем язык: {lang}")
                transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang])

                # Простое объединение текста
                text = ' '.join([item['text'] for item in transcript])

                return {
                    'text': text,
                    'language': lang,
                    'method': 'api',
                    'source': 'official_transcript'
                }
            except Exception as e:
                print(f"    Ошибка для {lang}: {e}")
                continue

        # Если не получилось с конкретными языками, пробуем любые доступные
        try:
            print("  Пробуем любые доступные субтитры...")
            transcript = YouTubeTranscriptApi.get_transcript(video_id)
            text = ' '.join([item['text'] for item in transcript])

            return {
                'text': text,
                'language': 'auto',
                'method': 'api',
                'source': 'official_transcript'
            }
        except Exception as e:
            print(f"    Ошибка при получении любых субтитров: {e}")

    except Exception as e:
        print(f"Общая ошибка API: {e}")
        return None

def get_transcript_via_ytdlp_with_cookies(url, languages=['ru', 'en']):
    """Получает субтитры через yt-dlp с использованием cookies из браузера"""
    try:
        ydl_opts = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': languages,
            'skip_download': True,
            'outtmpl': 'temp_%(title)s.%(ext)s',
            'cookiesfrombrowser': ('chrome',),  # Попробуем взять cookies из Chrome
            'extractor_args': {
                'youtube': {
                    'player_client': ['android', 'web'],
                    'player_skip': ['configs'],
                }
            },
            'no_warnings': True,
        }

        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)

            # Проверяем субтитры и автоматические субтитры
            subtitles = info.get('subtitles', {})
            auto_captions = info.get('automatic_captions', {})

            # Пробуем найти нужный язык
            for lang in languages:
                if lang in subtitles:
                    # Скачиваем субтитры
                    ydl_opts['subtitleslangs'] = [lang]
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl_sub:
                        ydl_sub.download([url])

                    # Ищем скачанный файл
                    title = info.get('title', 'video')
                    vtt_file = f"temp_{title}.{lang}.vtt"

                    if os.path.exists(vtt_file):
                        text = parse_vtt_file(vtt_file)
                        os.remove(vtt_file)  # Удаляем временный файл

                        return {
                            'text': text,
                            'language': lang,
                            'method': 'yt-dlp-cookies',
                            'source': 'manual_subtitles'
                        }

                elif lang in auto_captions:
                    # Скачиваем автоматические субтитры
                    ydl_opts['subtitleslangs'] = [lang]
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl_sub:
                        ydl_sub.download([url])

                    title = info.get('title', 'video')
                    vtt_file = f"temp_{title}.{lang}.vtt"

                    if os.path.exists(vtt_file):
                        text = parse_vtt_file(vtt_file)
                        os.remove(vtt_file)

                        return {
                            'text': text,
                            'language': lang,
                            'method': 'yt-dlp-cookies',
                            'source': 'auto_subtitles'
                        }

    except Exception as e:
        print(f"Ошибка yt-dlp с cookies: {e}")
        return None

def get_transcript_via_ytdlp(url, languages=['ru', 'en']):
    """Получает субтитры через yt-dlp"""
    try:
        ydl_opts = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': languages,
            'skip_download': True,
            'outtmpl': 'temp_%(title)s.%(ext)s',
            'extractor_args': {
                'youtube': {
                    'player_client': ['android', 'web'],
                    'player_skip': ['configs'],
                }
            },
            'no_warnings': True,
            'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'headers': {
                'Accept-Language': 'en-US,en;q=0.9,ru;q=0.8',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            },
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            
            # Проверяем субтитры и автоматические субтитры
            subtitles = info.get('subtitles', {})
            auto_captions = info.get('automatic_captions', {})
            
            # Пробуем найти нужный язык
            for lang in languages:
                if lang in subtitles:
                    # Скачиваем субтитры
                    ydl_opts['subtitleslangs'] = [lang]
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl_sub:
                        ydl_sub.download([url])
                    
                    # Ищем скачанный файл
                    title = info.get('title', 'video')
                    vtt_file = f"temp_{title}.{lang}.vtt"
                    
                    if os.path.exists(vtt_file):
                        text = parse_vtt_file(vtt_file)
                        os.remove(vtt_file)  # Удаляем временный файл
                        
                        return {
                            'text': text,
                            'language': lang,
                            'method': 'yt-dlp',
                            'source': 'manual_subtitles'
                        }
                
                elif lang in auto_captions:
                    # Скачиваем автоматические субтитры
                    ydl_opts['subtitleslangs'] = [lang]
                    with yt_dlp.YoutubeDL(ydl_opts) as ydl_sub:
                        ydl_sub.download([url])
                    
                    title = info.get('title', 'video')
                    vtt_file = f"temp_{title}.{lang}.vtt"
                    
                    if os.path.exists(vtt_file):
                        text = parse_vtt_file(vtt_file)
                        os.remove(vtt_file)
                        
                        return {
                            'text': text,
                            'language': lang,
                            'method': 'yt-dlp',
                            'source': 'auto_subtitles'
                        }
        
    except Exception as e:
        print(f"Ошибка yt-dlp: {e}")
        return None

def parse_vtt_file(vtt_file_path):
    """Парсит VTT файл в чистый текст"""
    if not os.path.exists(vtt_file_path):
        return None
    
    with open(vtt_file_path, 'r', encoding='utf-8') as file:
        content = file.read()
    
    lines = content.split('\n')
    text_lines = []
    
    for line in lines:
        line = line.strip()
        
        # Пропускаем служебные строки
        if (not line or 
            line.startswith('WEBVTT') or 
            line.startswith('Kind:') or 
            line.startswith('Language:') or 
            '-->' in line or
            re.match(r'^\d+$', line)):
            continue
        
        # Очищаем от HTML тегов и временных меток
        clean_line = re.sub(r'<[^>]+>', '', line)
        clean_line = re.sub(r'<\d+:\d+:\d+\.\d+>', '', clean_line)
        
        if clean_line and clean_line not in text_lines:
            text_lines.append(clean_line)
    
    full_text = ' '.join(text_lines)
    full_text = re.sub(r'\s+', ' ', full_text).strip()
    
    return full_text

def get_video_transcript(url, languages=['ru', 'en']):
    """
    Универсальная функция для получения расшифровки видео
    Пробует разные методы в порядке приоритета
    """
    video_id = extract_video_id(url)
    if not video_id:
        return None
    
    print(f"Получаем расшифровку для видео: {video_id}")
    
    # Метод 1: Пробуем официальную расшифровку через API
    print("Пробуем официальную расшифровку...")
    result = get_transcript_via_api(video_id, languages)
    if result:
        print(f"✓ Получена {result['source']} на языке: {result['language']}")
        return result
    
    # Метод 2: Пробуем субтитры через yt-dlp с cookies
    print("Пробуем субтитры через yt-dlp с cookies...")
    result = get_transcript_via_ytdlp_with_cookies(url, languages)
    if result:
        print(f"✓ Получены {result['source']} на языке: {result['language']}")
        return result

    # Метод 3: Пробуем субтитры через yt-dlp без cookies
    print("Пробуем субтитры через yt-dlp без cookies...")
    result = get_transcript_via_ytdlp(url, languages)
    if result:
        print(f"✓ Получены {result['source']} на языке: {result['language']}")
        return result

    print("✗ Не удалось получить расшифровку")
    return None

if __name__ == "__main__":
    # Тестируем
    url = "https://www.youtube.com/watch?v=0Uu_VJeVVfo"
    
    print("Тестируем универсальную функцию получения расшифровки")
    print("=" * 60)
    
    result = get_video_transcript(url, languages=['ru', 'en'])
    
    if result:
        print(f"\nРезультат:")
        print(f"Язык: {result['language']}")
        print(f"Метод: {result['method']}")
        print(f"Источник: {result['source']}")
        print(f"Длина текста: {len(result['text'])} символов")
        print(f"\nПервые 300 символов:")
        print(result['text'][:300] + "...")
        
        # Сохраняем результат
        filename = f"downloads/transcript_final_{result['language']}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(result['text'])
        print(f"\nСохранено в: {filename}")
    else:
        print("Не удалось получить расшифровку")