#!/usr/bin/env python3
"""
Разбивает большой файл субтитров на части и переводит их
"""
import re
import os

def clean_and_split_file(input_file, parts_count=10):
    """Разбивает файл на части для перевода"""
    print(f"Читаем файл: {input_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Размер файла: {len(content)} символов")
    
    # Очищаем от повторений
    print("Очищаем от повторений...")
    # Удаляем тройные повторения
    cleaned = re.sub(r'(\b[^.!?]*[.!?])\s+\1\s+\1', r'\1', content)
    # Удаляем HTML теги
    cleaned = re.sub(r'&gt;', '>', cleaned)
    cleaned = re.sub(r'&lt;', '<', cleaned)
    # Убираем лишние пробелы
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    
    print(f"После очистки: {len(cleaned)} символов")
    
    # Разбиваем на предложения
    sentences = re.split(r'[.!?]+', cleaned)
    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
    
    print(f"Получено {len(sentences)} предложений")
    
    # Разбиваем на части
    part_size = len(sentences) // parts_count
    
    for i in range(parts_count):
        start_idx = i * part_size
        if i == parts_count - 1:  # Последняя часть включает все оставшиеся предложения
            end_idx = len(sentences)
        else:
            end_idx = (i + 1) * part_size
        
        part_sentences = sentences[start_idx:end_idx]
        part_text = '. '.join(part_sentences) + '.'
        
        # Сохраняем часть
        part_filename = f"part_{i+1:02d}_en.txt"
        with open(part_filename, 'w', encoding='utf-8') as f:
            f.write(part_text)
        
        print(f"Создана часть {i+1}: {part_filename} ({len(part_text)} символов)")
        
        # Показываем начало части
        print(f"  Начало: {part_text[:100]}...")
    
    print(f"\n✓ Файл разбит на {parts_count} частей")
    print("Теперь вы можете перевести каждую часть отдельно:")
    print("1. Откройте каждый файл part_XX_en.txt")
    print("2. Скопируйте текст в Google Translate или другой переводчик")
    print("3. Сохраните перевод в файл part_XX_ru.txt")
    print("4. Запустите скрипт объединения")

def combine_translated_parts(parts_count=10, output_file="subtitles_0Uu_VJeVVfo.ru.txt"):
    """Объединяет переведенные части в один файл"""
    combined_text = []
    
    print("Объединяем переведенные части...")
    
    for i in range(1, parts_count + 1):
        part_filename = f"part_{i:02d}_ru.txt"
        
        if os.path.exists(part_filename):
            with open(part_filename, 'r', encoding='utf-8') as f:
                part_text = f.read().strip()
                combined_text.append(part_text)
                print(f"✓ Добавлена часть {i}: {part_filename}")
        else:
            print(f"✗ Не найден файл: {part_filename}")
    
    if combined_text:
        final_text = '\n\n'.join(combined_text)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_text)
        
        print(f"\n✓ Переведенный текст сохранен в: {output_file}")
        print(f"Размер: {len(final_text)} символов")
        
        # Показываем начало
        print(f"\nНачало переведенного текста:")
        print("-" * 50)
        print(final_text[:300] + "...")
        print("-" * 50)
    else:
        print("Не найдено переведенных частей!")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "combine":
        # Режим объединения
        combine_translated_parts()
    else:
        # Режим разбиения
        input_file = "subtitles_0Uu_VJeVVfo.en.txt"
        
        if not os.path.exists(input_file):
            print(f"Ошибка: файл {input_file} не найден!")
            exit(1)
        
        print("Разбиение файла субтитров на части")
        print("=" * 40)
        
        clean_and_split_file(input_file, parts_count=5)  # Разбиваем на 5 частей
