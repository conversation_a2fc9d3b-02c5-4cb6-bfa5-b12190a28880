#!/usr/bin/env python3
from youtube_transcript_api import YouTubeTranscript<PERSON>pi

def get_subtitles(video_id):
    """Простое получение субтитров"""
    try:
        # Пробуем английский
        print("Пробуем английские субтитры...")
        transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['en'])
        text = ' '.join([item['text'] for item in transcript])
        print(f"✓ Получены английские субтитры ({len(text)} символов)")
        return text, 'en'
    except:
        pass
    
    try:
        # Пробуем русские
        print("Пробуем русские субтитры...")
        transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=['ru'])
        text = ' '.join([item['text'] for item in transcript])
        print(f"✓ Получены русские субтитры ({len(text)} символов)")
        return text, 'ru'
    except:
        pass
    
    try:
        # Пробуем любые доступные
        print("Пробуем любые доступные субтитры...")
        transcript = YouTubeTranscriptApi.get_transcript(video_id)
        text = ' '.join([item['text'] for item in transcript])
        print(f"✓ Получены субтитры ({len(text)} символов)")
        return text, 'auto'
    except Exception as e:
        print(f"✗ Ошибка: {e}")
        return None, None

if __name__ == "__main__":
    video_id = "0Uu_VJeVVfo"  # Ваше видео
    
    text, lang = get_subtitles(video_id)
    
    if text:
        filename = f"downloads/subtitles_{lang}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"Субтитры сохранены в: {filename}")
        print(f"Первые 300 символов:")
        print(text[:300] + "...")
    else:
        print("Не удалось получить субтитры")
