#!/usr/bin/env python3
"""
Скрипт для перевода английских субтитров на русский язык поэтапно
"""
import re
import os
from googletrans import Translator

def clean_and_split_text(text):
    """Очищает текст от повторений и разбивает на предложения"""
    # Удаляем повторяющиеся фразы (когда одна фраза повторяется 3 раза подряд)
    # Паттерн для поиска повторений: одинаковые фразы, разделенные пробелами
    cleaned_text = re.sub(r'(\b[^.!?]*[.!?])\s+\1\s+\1', r'\1', text)
    
    # Дополнительная очистка для коротких повторений
    cleaned_text = re.sub(r'(\b\w+(?:\s+\w+){1,10})\s+\1\s+\1', r'\1', cleaned_text)
    
    # Разбиваем на предложения
    sentences = re.split(r'[.!?]+', cleaned_text)
    
    # Очищаем и фильтруем предложения
    clean_sentences = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 10:  # Игнорируем очень короткие фрагменты
            clean_sentences.append(sentence)
    
    return clean_sentences

def translate_text_batch(sentences, batch_size=5):
    """Переводит текст по частям для лучшего качества"""
    translator = Translator()
    translated_sentences = []
    
    print(f"Переводим {len(sentences)} предложений...")
    
    for i in range(0, len(sentences), batch_size):
        batch = sentences[i:i+batch_size]
        print(f"Переводим предложения {i+1}-{min(i+batch_size, len(sentences))}...")
        
        try:
            # Переводим каждое предложение отдельно для лучшего качества
            for sentence in batch:
                if sentence.strip():
                    # Переводим с английского на русский
                    result = translator.translate(sentence, src='en', dest='ru')
                    translated_sentences.append(result.text)
                    print(f"  ✓ Переведено: {sentence[:50]}...")
        except Exception as e:
            print(f"Ошибка при переводе: {e}")
            # В случае ошибки добавляем оригинальный текст
            translated_sentences.extend(batch)
    
    return translated_sentences

def format_translated_text(translated_sentences):
    """Форматирует переведенный текст"""
    # Объединяем предложения в абзацы
    formatted_text = ""
    
    for i, sentence in enumerate(translated_sentences):
        formatted_text += sentence.strip()
        
        # Добавляем точку если её нет
        if not sentence.strip().endswith(('.', '!', '?')):
            formatted_text += "."
        
        # Добавляем новую строку каждые 3-4 предложения для читаемости
        if (i + 1) % 3 == 0:
            formatted_text += "\n\n"
        else:
            formatted_text += " "
    
    return formatted_text.strip()

def translate_subtitles_file(input_file, output_file):
    """Основная функция для перевода файла субтитров"""
    print(f"Читаем файл: {input_file}")
    
    # Читаем исходный файл
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Размер исходного текста: {len(content)} символов")
    
    # Очищаем и разбиваем текст
    print("Очищаем текст от повторений...")
    sentences = clean_and_split_text(content)
    print(f"Получено {len(sentences)} предложений для перевода")
    
    # Показываем первые несколько предложений
    print("\nПервые 3 предложения для перевода:")
    for i, sentence in enumerate(sentences[:3]):
        print(f"{i+1}. {sentence}")
    
    # Переводим текст
    print("\nНачинаем перевод...")
    translated_sentences = translate_text_batch(sentences)
    
    # Форматируем результат
    print("Форматируем переведенный текст...")
    formatted_text = format_translated_text(translated_sentences)
    
    # Сохраняем результат
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(formatted_text)
    
    print(f"\n✓ Перевод завершен!")
    print(f"Результат сохранен в: {output_file}")
    print(f"Размер переведенного текста: {len(formatted_text)} символов")
    
    # Показываем начало переведенного текста
    print(f"\nНачало переведенного текста:")
    print("-" * 50)
    print(formatted_text[:500] + "...")
    print("-" * 50)

if __name__ == "__main__":
    input_file = "subtitles_0Uu_VJeVVfo.en.txt"
    output_file = "subtitles_0Uu_VJeVVfo.ru.txt"
    
    if not os.path.exists(input_file):
        print(f"Ошибка: файл {input_file} не найден!")
        exit(1)
    
    print("Скрипт для перевода английских субтитров на русский")
    print("=" * 60)
    
    try:
        translate_subtitles_file(input_file, output_file)
    except Exception as e:
        print(f"Ошибка: {e}")
        print("Попробуйте установить googletrans: pip install googletrans==4.0.0rc1")
