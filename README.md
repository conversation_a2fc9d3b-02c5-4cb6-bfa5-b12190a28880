# Извлечение субтитров с YouTube

Это руководство описывает бесплатный способ извлечения субтитров с YouTube видео, включая русские субтитры.

## Необходимые инструменты

- **yt-dlp** - мощный инструмент для скачивания видео и субтитров с YouTube
- **Python 3** - для запуска скрипта конвертации субтитров
- **Браузер Firefox** (или другой браузер) - для использования куки

## Установка yt-dlp

### Linux
```bash
# Через pip
pip install yt-dlp

# Или через apt (на Ubuntu/De<PERSON>)
sudo apt install yt-dlp
```

### Windows
```bash
# Через pip
pip install yt-dlp

# Или скачать исполняемый файл с официального сайта
# https://github.com/yt-dlp/yt-dlp/releases
```

## Скачивание субтитров

### Базовая команда
```bash
yt-dlp --skip-download --write-subs --write-auto-subs --sub-langs ЯЗЫК --output "субтитры_%(id)s.%(ext)s" ID_ВИДЕО
```

Где:
- `ЯЗЫК` - код языка (например, `ru` для русского, `en` для английского)
- `ID_ВИДЕО` - идентификатор YouTube видео (например, `Y5WFPNg-j9Q`)

### Обход ограничений YouTube

Если YouTube блокирует доступ к субтитрам, используйте куки из браузера:

```bash
yt-dlp --skip-download --write-subs --write-auto-subs --sub-langs ru --sub-format vtt --output "субтитры_%(id)s.%(ext)s" ID_ВИДЕО --cookies-from-browser firefox
```

Эта команда использует куки из браузера Firefox для обхода ограничений.

## Конвертация субтитров из VTT в текстовый формат

Создайте файл `vtt_to_txt.py` со следующим содержимым:

```python
#!/usr/bin/env python3
"""
Скрипт для преобразования субтитров из формата VTT в простой текстовый формат.
"""

import re
import sys

def vtt_to_text(vtt_file, txt_file):
    with open(vtt_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Удаляем заголовок VTT
    content = re.sub(r'^WEBVTT\n.*?\n\n', '', content, flags=re.DOTALL)
    
    # Удаляем временные метки и форматирование
    content = re.sub(r'\d{2}:\d{2}:\d{2}\.\d{3} --> \d{2}:\d{2}:\d{2}\.\d{3}.*?\n', '', content)
    content = re.sub(r'<\d{2}:\d{2}:\d{2}\.\d{3}>', '', content)
    content = re.sub(r'<c>', '', content)
    content = re.sub(r'</c>', '', content)
    content = re.sub(r'align:start position:0%', '', content)
    
    # Удаляем пустые строки
    content = re.sub(r'^\s*\n', '', content, flags=re.MULTILINE)
    
    # Объединяем текст
    lines = content.split('\n')
    text_lines = []
    current_line = ""
    
    for line in lines:
        if line.strip():
            if current_line:
                current_line += " " + line.strip()
            else:
                current_line = line.strip()
        else:
            if current_line:
                text_lines.append(current_line)
                current_line = ""
    
    if current_line:
        text_lines.append(current_line)
    
    # Записываем результат в файл
    with open(txt_file, 'w', encoding='utf-8') as f:
        for line in text_lines:
            f.write(line + '\n')

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Использование: python vtt_to_txt.py <vtt_file> [txt_file]")
        sys.exit(1)
    
    vtt_file = sys.argv[1]
    txt_file = sys.argv[2] if len(sys.argv) > 2 else vtt_file.replace('.vtt', '.txt')
    
    vtt_to_text(vtt_file, txt_file)
    print(f"Субтитры преобразованы и сохранены в {txt_file}")
```

Затем запустите скрипт для конвертации:

```bash
python vtt_to_txt.py субтитры_ID_ВИДЕО.ru.vtt
```

## Полный пример

### Скачивание русских субтитров

Для скачивания русских субтитров с видео ID `Y5WFPNg-j9Q`:

```bash
# Шаг 1: Скачать субтитры
yt-dlp --skip-download --write-subs --write-auto-subs --sub-langs ru --sub-format vtt --output "субтитры_%(id)s.%(ext)s" Y5WFPNg-j9Q --cookies-from-browser firefox

# Шаг 2: Конвертировать в текстовый формат
python vtt_to_txt.py субтитры_Y5WFPNg-j9Q.ru.vtt
```

### Скачивание английских субтитров

Для скачивания английских субтитров с видео ID `0Uu_VJeVVfo` (пример с OpenAI презентацией):

```bash
# Шаг 1: Создать виртуальное окружение (рекомендуется для новой версии yt-dlp)
python3 -m venv venv
source venv/bin/activate

# Шаг 2: Установить последнюю версию yt-dlp
pip install --upgrade yt-dlp

# Шаг 3: Скачать английские субтитры
yt-dlp --skip-download --write-auto-subs --sub-langs=en --sub-format=vtt --output=subtitles_0Uu_VJeVVfo https://www.youtube.com/watch?v=0Uu_VJeVVfo

# Шаг 4: Конвертировать в чистый текст с помощью улучшенного конвертера
python3 clean_vtt_converter.py subtitles_0Uu_VJeVVfo.en.vtt subtitles_0Uu_VJeVVfo.en.txt
```

**Примечание**: Для некоторых видео может потребоваться использование `--write-auto-subs` вместо `--write-subs`, если оригинальные субтитры недоступны.

## Дополнительные опции

### Список доступных языков субтитров

Чтобы узнать, какие языки субтитров доступны для видео:

```bash
yt-dlp --list-subs --skip-download ID_ВИДЕО
```

### Скачивание субтитров на нескольких языках

```bash
yt-dlp --skip-download --write-subs --write-auto-subs --sub-langs "ru,en" --output "субтитры_%(id)s.%(ext)s" ID_ВИДЕО --cookies-from-browser firefox
```

### Другие форматы субтитров

Доступные форматы: `vtt`, `srt`, `ass`, `lrc`, `json`

```bash
yt-dlp --skip-download --write-subs --write-auto-subs --sub-langs ru --sub-format srt --output "субтитры_%(id)s.%(ext)s" ID_ВИДЕО --cookies-from-browser firefox
```

## Устранение неполадок

1. **Ошибка "Sign in to confirm you're not a bot"**:
   - Используйте опцию `--cookies-from-browser`
   - Убедитесь, что вы вошли в свой аккаунт YouTube в указанном браузере

2. **Ошибка "HTTP Error 429: Too Many Requests"**:
   - Подождите некоторое время и попробуйте снова
   - Используйте VPN

3. **Субтитры не найдены**:
   - Проверьте, есть ли субтитры на нужном языке с помощью `--list-subs`
   - Попробуйте скачать автоматически сгенерированные субтитры

## Примечания

- Этот метод работает для большинства видео на YouTube, включая те, которые имеют автоматически сгенерированные субтитры
- Качество автоматически сгенерированных субтитров может быть не идеальным
- Метод является полностью бесплатным и не требует сторонних сервисов
