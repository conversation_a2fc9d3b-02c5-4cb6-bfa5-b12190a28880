import { Link } from "react-router-dom";
import type { Route } from "./+types/home";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Транскрипт YouTube - Главная" },
    { name: "description", content: "Извлечение субтитров с YouTube видео" },
  ];
}

export default function Home() {
  return (
    <div className="container mx-auto max-w-2xl py-10">
      <Card className="border-none shadow-none">
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Транскрипт YouTube</CardTitle>
          <p className="text-sm text-muted-foreground">
            Извлечение субтитров с YouTube видео
          </p>
        </CardHeader>
        <CardContent className="text-center">
          <p className="mb-6">
            Наш сервис позволяет извлекать субтитры с YouTube видео и сохранять их в текстовом формате.
          </p>
          <Button asChild>
            <Link to="/subtitles">Перейти к извлечению субтитров</Link>
          </Button>
        </CardContent>
      </Card>
      
      <footer className="mt-10 text-center">
        <p className="text-sm">&copy; 2025 Транскрипт YouTube</p>
        <p className="text-xs text-muted-foreground">Сервис не связан с YouTube и Google</p>
      </footer>
    </div>
  );
}
