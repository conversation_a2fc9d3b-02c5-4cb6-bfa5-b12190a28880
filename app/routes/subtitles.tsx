import { useState } from "react";
import type { Route } from "./+types/subtitles";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "~/components/ui/select";
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from "~/components/ui/card";
import { extractSubtitles } from "~/api/youtube-api";

export function meta({}: Route.MetaArgs) {
  return [
    { title: "Транскрипт YouTube - Извлечение субтитров" },
    { name: "description", content: "Извлечение субтитров с YouTube видео" },
  ];
}

export default function Subtitles() {
  const [videoUrl, setVideoUrl] = useState("");
  const [language, setLanguage] = useState("ru");
  const [isLoading, setIsLoading] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState("");
  const [filename, setFilename] = useState("");
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    
    try {
      const data = await extractSubtitles(videoUrl, language);
      setDownloadUrl(data.download_url);
      setFilename(data.filename);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Произошла ошибка");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto max-w-2xl py-10">
      <Card className="border-none shadow-none">
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Транскрипт YouTube видео</CardTitle>
          <p className="text-sm text-muted-foreground">
            Извлечение автоматически сгенерированных и созданных вручную субтитров
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <label htmlFor="video-url" className="text-sm font-medium">
                Ссылка на видео
              </label>
              <Input
                id="video-url"
                placeholder="https://www.youtube.com/watch?v=..."
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                required
              />
              <p className="text-xs text-muted-foreground">
                Вставьте ссылку на YouTube видео
              </p>
            </div>
            
            <div className="space-y-2">
              <label htmlFor="language" className="text-sm font-medium">
                Язык
              </label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger id="language" className="w-full">
                  <SelectValue placeholder="Выберите язык" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ru">Русский</SelectItem>
                  <SelectItem value="en">Английский</SelectItem>
                  <SelectItem value="fr">Французский</SelectItem>
                  <SelectItem value="de">Немецкий</SelectItem>
                  <SelectItem value="es">Испанский</SelectItem>
                  <SelectItem value="it">Итальянский</SelectItem>
                  <SelectItem value="ja">Японский</SelectItem>
                  <SelectItem value="ko">Корейский</SelectItem>
                  <SelectItem value="zh-Hans">Китайский (упрощенный)</SelectItem>
                  <SelectItem value="zh-Hant">Китайский (традиционный)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Обработка..." : "Получить текст"}
            </Button>
            
            {error && (
              <p className="text-sm text-red-500">{error}</p>
            )}
          </form>
          
          {downloadUrl && (
            <div className="mt-6 text-center">
              <div className="mb-4">
                <span className="inline-flex items-center">
                  <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>Готово</span>
                </span>
              </div>
              <Button asChild className="w-full">
                <a href={downloadUrl} download>
                  Скачать файл
                </a>
              </Button>
              <p className="mt-2 text-xs text-muted-foreground">{filename}</p>
              <p className="text-xs text-muted-foreground">Ссылка действительна 24 часа</p>
            </div>
          )}
        </CardContent>
      </Card>
      
      <div className="mt-10 space-y-6">
        <div className="space-y-2">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h2 className="text-sm font-medium">Поддержать проект</h2>
          </div>
          <p className="text-xs text-muted-foreground">
            Сервис работает бесплатно. Если он вам полезен, вы можете поддержать его развитие.
          </p>
          <Button variant="outline" size="sm" asChild className="mt-2">
            <a href="https://yoomoney.ru/to/****************" target="_blank" rel="noopener noreferrer">
              <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
              </svg>
              Сделать донат
            </a>
          </Button>
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center">
            <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
            <h2 className="text-sm font-medium">Для разработчиков</h2>
          </div>
          <p className="text-xs text-muted-foreground">
            Доступно API для интеграции. Для получения доступа свяжитесь с нами.
          </p>
        </div>
      </div>
      
      <footer className="mt-10 text-center">
        <p className="text-sm">&copy; 2025 Транскрипт YouTube</p>
        <p className="text-xs text-muted-foreground">Сервис не связан с YouTube и Google</p>
      </footer>
    </div>
  );
}
