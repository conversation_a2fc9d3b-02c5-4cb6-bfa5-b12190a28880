/**
 * API-клиент для взаимодействия с FastAPI бэкендом
 */

export interface SubtitlesResponse {
  download_url: string;
  filename: string;
  expires_in: string;
}

export interface SubtitlesRequest {
  video_url: string;
  language: string;
}

/**
 * Извлекает субтитры из YouTube видео
 * @param videoUrl URL видео на YouTube
 * @param language Код языка субтитров
 * @returns Объект с URL для скачивания и именем файла
 */
export async function extractSubtitles(videoUrl: string, language: string): Promise<SubtitlesResponse> {
  const response = await fetch('/api/extract', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      video_url: videoUrl,
      language: language,
    }),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.detail || 'Ошибка при извлечении субтитров');
  }

  return response.json();
}
