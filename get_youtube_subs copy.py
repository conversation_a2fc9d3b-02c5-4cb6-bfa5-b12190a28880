#!/usr/bin/env python3
"""
Скрипт для автоматического извлечения русских субтитров с YouTube.
Просто введите ID видео, и скрипт скачает субтитры и сохранит их в текстовом формате.

Использование:
    python get_youtube_subs.py <ID_видео>

Пример:
    python get_youtube_subs.py Y5WFPNg-j9Q
"""

import sys
import os
import re
import subprocess
import tempfile

def check_yt_dlp_installed():
    """Проверяет, установлен ли yt-dlp."""
    try:
        subprocess.run(['yt-dlp', '--version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except FileNotFoundError:
        return False

def download_subtitles(video_id, output_file, language='ru'):
    """Скачивает субтитры с YouTube."""
    # Создаем базовое имя файла без расширения
    output_base = os.path.splitext(output_file)[0]

    cmd = [
        'yt-dlp',
        '--skip-download',
        '--write-subs',
        '--write-auto-subs',
        f'--sub-langs={language}',
        '--sub-format=vtt',
        f'--output={output_base}',
        f'https://www.youtube.com/watch?v={video_id}',
        '--cookies-from-browser', 'firefox',
        '--verbose'
    ]

    try:
        print("Выполняется команда:", " ".join(cmd))
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        print(f"Вывод команды: {result.stdout}")

        if result.returncode != 0:
            print(f"Ошибка при скачивании субтитров: {result.stderr}")
            return False

        # Ищем скачанный файл субтитров
        directory = os.path.dirname(output_base) or '.'
        filename_prefix = os.path.basename(output_base)

        print(f"Ищем файлы субтитров в директории: {directory}")
        for file in os.listdir(directory):
            if file.startswith(filename_prefix) and file.endswith(f'.{language}.vtt'):
                found_file = os.path.join(directory, file)
                print(f"Найден файл субтитров: {found_file}")

                # Копируем найденный файл в нужное место
                with open(found_file, 'r', encoding='utf-8') as src:
                    with open(output_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())

                # Удаляем оригинальный файл
                os.remove(found_file)
                return True

        print("Файл субтитров не найден после скачивания")
        return False
    except Exception as e:
        print(f"Произошла ошибка: {str(e)}")
        return False

def convert_vtt_to_text(vtt_file, txt_file):
    """Конвертирует субтитры из формата VTT в текстовый формат."""
    try:
        # Проверяем существование файла
        if not os.path.exists(vtt_file):
            print(f"Файл {vtt_file} не существует")
            return False

        # Проверяем размер файла
        file_size = os.path.getsize(vtt_file)
        print(f"Размер файла субтитров: {file_size} байт")

        if file_size == 0:
            print("Файл субтитров пуст")
            return False

        with open(vtt_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"Прочитано {len(content)} символов из файла")
            print("Первые 100 символов файла:")
            print(content[:100])

        # Проверяем, является ли файл действительно VTT
        if not content.startswith('WEBVTT'):
            print("Файл не является корректным VTT файлом")
            # Попробуем другой подход - прямое извлечение текста
            lines = content.split('\n')
            text_lines = []

            for line in lines:
                # Пропускаем строки с временными метками и пустые строки
                if '-->' in line or not line.strip():
                    continue
                text_lines.append(line.strip())

            # Записываем результат в файл
            with open(txt_file, 'w', encoding='utf-8') as f:
                for line in text_lines:
                    f.write(line + '\n')

            print(f"Извлечено {len(text_lines)} строк текста напрямую")
            return len(text_lines) > 0

        # Стандартная обработка VTT
        # Удаляем заголовок VTT
        content = re.sub(r'^WEBVTT\n.*?\n\n', '', content, flags=re.DOTALL)

        # Удаляем временные метки и форматирование
        content = re.sub(r'\d{2}:\d{2}:\d{2}\.\d{3} --> \d{2}:\d{2}:\d{2}\.\d{3}.*?\n', '', content)
        content = re.sub(r'<\d{2}:\d{2}:\d{2}\.\d{3}>', '', content)
        content = re.sub(r'<c>', '', content)
        content = re.sub(r'</c>', '', content)
        content = re.sub(r'align:start position:0%', '', content)

        # Удаляем пустые строки
        content = re.sub(r'^\s*\n', '', content, flags=re.MULTILINE)

        # Объединяем текст
        lines = content.split('\n')
        text_lines = []
        current_line = ""

        for line in lines:
            if line.strip():
                if current_line:
                    current_line += " " + line.strip()
                else:
                    current_line = line.strip()
            else:
                if current_line:
                    text_lines.append(current_line)
                    current_line = ""

        if current_line:
            text_lines.append(current_line)

        print(f"Извлечено {len(text_lines)} строк текста")

        # Записываем результат в файл
        with open(txt_file, 'w', encoding='utf-8') as f:
            for line in text_lines:
                f.write(line + '\n')

        return len(text_lines) > 0
    except Exception as e:
        print(f"Ошибка при конвертации субтитров: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Основная функция скрипта."""
    # Проверка наличия yt-dlp
    if not check_yt_dlp_installed():
        print("Ошибка: yt-dlp не установлен.")
        print("Установите его командой: pip install yt-dlp")
        return 1

    # Проверка наличия аргумента (ID видео)
    if len(sys.argv) < 2:
        print(f"Использование: {sys.argv[0]} <ID_видео>")
        print(f"Пример: {sys.argv[0]} Y5WFPNg-j9Q")
        return 1

    # Получение ID видео
    video_id = sys.argv[1]

    # Удаление префикса URL, если он есть
    if "youtube.com" in video_id or "youtu.be" in video_id:
        if "v=" in video_id:
            video_id = video_id.split("v=")[1].split("&")[0]
        elif "youtu.be/" in video_id:
            video_id = video_id.split("youtu.be/")[1].split("?")[0]

    print(f"Извлечение русских субтитров для видео с ID: {video_id}")

    # Определяем имена файлов
    temp_dir = tempfile.mkdtemp()
    temp_vtt = os.path.join(temp_dir, f"subtitles_{video_id}")
    output_txt = f"subtitles_{video_id}.ru.txt"

    try:
        # Скачивание субтитров
        if not download_subtitles(video_id, temp_vtt):
            print("Не удалось скачать субтитры. Пробуем альтернативный метод...")

            # Альтернативный метод - прямое использование yt-dlp
            cmd = [
                'yt-dlp',
                '--skip-download',
                '--write-subs',
                '--write-auto-subs',
                '--sub-langs=ru',
                '--sub-format=vtt',
                f'--output=subtitles_{video_id}',
                f'https://www.youtube.com/watch?v={video_id}'
            ]

            print("Выполняется команда:", " ".join(cmd))
            subprocess.run(cmd, check=False)

            # Ищем созданный файл
            possible_files = [
                f"subtitles_{video_id}.ru.vtt",
                f"subtitles_{video_id}.ru-orig.vtt",
                f"subtitles_{video_id}.ru.ru.vtt"
            ]

            found_file = None
            for file in possible_files:
                if os.path.exists(file):
                    found_file = file
                    break

            if not found_file:
                print("Не удалось найти файл субтитров после скачивания.")
                return 1

            temp_vtt = found_file
            print(f"Найден файл субтитров: {temp_vtt}")

        print("Субтитры успешно скачаны.")

        # Конвертация в текстовый формат
        if not convert_vtt_to_text(temp_vtt, output_txt):
            print("Не удалось конвертировать субтитры. Пробуем прямой метод...")

            # Прямой метод извлечения текста из субтитров
            try:
                with open(temp_vtt, 'r', encoding='utf-8') as f:
                    content = f.read()

                # Извлекаем только текст, игнорируя временные метки
                lines = content.split('\n')
                text_lines = []

                for i in range(len(lines)):
                    line = lines[i].strip()
                    # Пропускаем строки с временными метками и пустые строки
                    if '-->' in line or not line or line.startswith('WEBVTT'):
                        continue
                    # Пропускаем номера субтитров
                    if line.isdigit() and i+1 < len(lines) and '-->' in lines[i+1]:
                        continue
                    text_lines.append(line)

                # Записываем результат в файл
                with open(output_txt, 'w', encoding='utf-8') as f:
                    for line in text_lines:
                        f.write(line + '\n')

                print(f"Извлечено {len(text_lines)} строк текста напрямую")
            except Exception as e:
                print(f"Ошибка при прямом извлечении текста: {str(e)}")
                return 1

        print("Субтитры успешно конвертированы в текстовый формат.")

        # Удаление временных файлов
        try:
            if os.path.exists(temp_vtt):
                os.remove(temp_vtt)
            if os.path.exists(temp_dir):
                os.rmdir(temp_dir)
        except:
            pass

        print(f"Готово! Субтитры сохранены в файле: {output_txt}")

        # Показать первые 5 строк субтитров
        try:
            with open(output_txt, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                line_count = len(lines)

                print("\nПредпросмотр субтитров:")
                print("----------------------")
                for i in range(min(5, line_count)):
                    print(lines[i].strip())
                print("----------------------")
                print(f"Всего строк: {line_count}")
        except Exception as e:
            print(f"Ошибка при чтении файла субтитров: {str(e)}")

        return 0
    except Exception as e:
        print(f"Произошла непредвиденная ошибка: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
