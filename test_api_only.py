#!/usr/bin/env python3
import re
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api.formatters import TextFormatter

def extract_video_id(url):
    """Извлекает video_id из YouTube URL"""
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([^&\n?#]+)',
        r'youtube\.com/watch\?.*v=([^&\n?#]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None

def test_api_transcript(url, languages=['en', 'ru']):
    """Тестирует только YouTube Transcript API"""
    video_id = extract_video_id(url)
    if not video_id:
        print("Не удалось извлечь video_id из URL")
        return None
    
    print(f"Video ID: {video_id}")
    
    try:
        # Получаем список доступных расшифровок
        transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
        
        print("Доступные расшифровки:")
        for transcript in transcript_list:
            print(f"  - {transcript.language_code}: {transcript.language}")
            if transcript.is_generated:
                print("    (автоматически сгенерированные)")
            else:
                print("    (ручные)")
        
        # Пробуем получить расшифровку на нужных языках
        for lang in languages:
            try:
                print(f"\nПробуем получить расшифровку на языке: {lang}")
                transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang])
                
                # Форматируем в обычный текст
                formatter = TextFormatter()
                text = formatter.format_transcript(transcript)
                
                print(f"✓ Успешно получена расшифровка на языке: {lang}")
                print(f"Длина текста: {len(text)} символов")
                print(f"Первые 200 символов:")
                print(text[:200] + "...")
                
                return {
                    'text': text,
                    'language': lang,
                    'method': 'api',
                    'source': 'official_transcript'
                }
            except Exception as e:
                print(f"✗ Ошибка для языка {lang}: {e}")
                continue
        
        print("\nНе найдены расшифровки на запрошенных языках")
        return None
                
    except Exception as e:
        print(f"Общая ошибка API: {e}")
        return None

if __name__ == "__main__":
    # Тестируем на вашем видео
    url = "https://www.youtube.com/watch?v=0Uu_VJeVVfo"
    
    print("Тестируем только YouTube Transcript API")
    print("=" * 50)
    
    result = test_api_transcript(url, languages=['en', 'ru'])
    
    if result:
        print(f"\n✓ Получена расшифровка:")
        print(f"Язык: {result['language']}")
        print(f"Метод: {result['method']}")
        print(f"Источник: {result['source']}")
        
        # Сохраняем результат
        filename = f"downloads/api_test_{result['language']}.txt"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(result['text'])
        print(f"Сохранено в: {filename}")
    else:
        print("\n✗ Не удалось получить расшифровку через API")
