#!/usr/bin/env python3
import re
import os
from youtube_transcript_api import YouTubeTranscriptApi
import yt_dlp

def extract_video_id(url):
    """Извлекает video_id из YouTube URL"""
    patterns = [
        r'(?:youtube\.com/watch\?v=|youtu\.be/|youtube\.com/embed/)([^&\n?#]+)',
        r'youtube\.com/watch\?.*v=([^&\n?#]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None

def get_subtitles_via_api(video_id, languages=['en', 'ru']):
    """Получает субтитры с временными метками через API"""
    try:
        for lang in languages:
            try:
                print(f"Пробуем получить субтитры на языке: {lang}")
                transcript = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang])
                
                return {
                    'subtitles': transcript,
                    'language': lang,
                    'method': 'api'
                }
            except Exception as e:
                print(f"Ошибка для {lang}: {e}")
                continue
        
        # Пробуем любые доступные
        try:
            print("Пробуем любые доступные субтитры...")
            transcript = YouTubeTranscriptApi.get_transcript(video_id)
            return {
                'subtitles': transcript,
                'language': 'auto',
                'method': 'api'
            }
        except Exception as e:
            print(f"Ошибка: {e}")
            return None
                
    except Exception as e:
        print(f"Общая ошибка API: {e}")
        return None

def get_subtitles_via_ytdlp(url, languages=['en', 'ru']):
    """Скачивает субтитры через yt-dlp"""
    try:
        ydl_opts = {
            'writesubtitles': True,
            'writeautomaticsub': True,
            'subtitleslangs': languages,
            'skip_download': True,
            'outtmpl': 'downloads/%(title)s.%(ext)s',
            'no_warnings': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=False)
            title = info.get('title', 'video')
            
            # Скачиваем субтитры
            ydl.download([url])
            
            # Ищем скачанные файлы
            for lang in languages:
                vtt_file = f"downloads/{title}.{lang}.vtt"
                if os.path.exists(vtt_file):
                    return {
                        'file': vtt_file,
                        'language': lang,
                        'method': 'yt-dlp',
                        'title': title
                    }
            
            # Проверяем автоматические субтитры
            for lang in languages:
                auto_vtt_file = f"downloads/{title}.{lang}.vtt"
                if os.path.exists(auto_vtt_file):
                    return {
                        'file': auto_vtt_file,
                        'language': lang,
                        'method': 'yt-dlp-auto',
                        'title': title
                    }
        
    except Exception as e:
        print(f"Ошибка yt-dlp: {e}")
        return None

def save_subtitles_as_srt(subtitles_data, filename):
    """Сохраняет субтитры в формате SRT"""
    if subtitles_data['method'] == 'api':
        # Конвертируем из API формата в SRT
        with open(filename, 'w', encoding='utf-8') as f:
            for i, item in enumerate(subtitles_data['subtitles'], 1):
                start_time = format_time(item['start'])
                end_time = format_time(item['start'] + item['duration'])
                
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{item['text']}\n\n")
    else:
        # Копируем VTT файл
        print(f"VTT файл уже скачан: {subtitles_data['file']}")

def format_time(seconds):
    """Форматирует время в формат SRT (HH:MM:SS,mmm)"""
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    secs = int(seconds % 60)
    millisecs = int((seconds % 1) * 1000)
    
    return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"

def get_video_subtitles(url, languages=['en', 'ru']):
    """Основная функция для получения субтитров"""
    video_id = extract_video_id(url)
    if not video_id:
        print("Не удалось извлечь video_id")
        return None
    
    print(f"Получаем субтитры для видео: {video_id}")
    
    # Метод 1: API
    print("Пробуем YouTube Transcript API...")
    result = get_subtitles_via_api(video_id, languages)
    if result:
        print(f"✓ Получены субтитры через API на языке: {result['language']}")
        
        # Сохраняем в SRT формате
        srt_filename = f"downloads/subtitles_{result['language']}.srt"
        save_subtitles_as_srt(result, srt_filename)
        print(f"Сохранено в: {srt_filename}")
        
        return result
    
    # Метод 2: yt-dlp
    print("Пробуем yt-dlp...")
    result = get_subtitles_via_ytdlp(url, languages)
    if result:
        print(f"✓ Получены субтитры через yt-dlp на языке: {result['language']}")
        print(f"Файл: {result['file']}")
        return result
    
    print("✗ Не удалось получить субтитры")
    return None

if __name__ == "__main__":
    url = "https://www.youtube.com/watch?v=0Uu_VJeVVfo"
    
    print("Получаем субтитры с временными метками")
    print("=" * 50)
    
    # Создаем папку downloads если её нет
    os.makedirs('downloads', exist_ok=True)
    
    result = get_video_subtitles(url, languages=['en', 'ru'])
    
    if result:
        print(f"\n✓ Субтитры получены!")
        print(f"Язык: {result['language']}")
        print(f"Метод: {result['method']}")
        
        if result['method'] == 'api':
            print(f"Количество субтитров: {len(result['subtitles'])}")
            print("Первые 3 субтитра:")
            for i, item in enumerate(result['subtitles'][:3]):
                start = format_time(item['start'])
                end = format_time(item['start'] + item['duration'])
                print(f"  {start} --> {end}")
                print(f"  {item['text']}")
                print()
    else:
        print("Не удалось получить субтитры")
