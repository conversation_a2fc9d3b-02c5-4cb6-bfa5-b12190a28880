#!/usr/bin/env python3
"""
Простой скрипт для перевода английских субтитров на русский язык
Разбивает текст на небольшие части и переводит их поэтапно
"""
import re
import os
import time
from googletrans import Translator

def clean_text(text):
    """Очищает текст от повторений"""
    # Удаляем тройные повторения
    cleaned = re.sub(r'(\b[^.!?]*[.!?])\s+\1\s+\1', r'\1', text)
    # Удаляем HTML теги
    cleaned = re.sub(r'&gt;', '>', cleaned)
    cleaned = re.sub(r'&lt;', '<', cleaned)
    # Убираем лишние пробелы
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    return cleaned

def split_into_chunks(text, max_length=1000):
    """Разбивает текст на части по предложениям"""
    sentences = re.split(r'[.!?]+', text)
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        sentence = sentence.strip()
        if not sentence:
            continue
            
        # Если добавление предложения не превысит лимит
        if len(current_chunk + sentence) < max_length:
            if current_chunk:
                current_chunk += ". " + sentence
            else:
                current_chunk = sentence
        else:
            # Сохраняем текущий чанк и начинаем новый
            if current_chunk:
                chunks.append(current_chunk + ".")
            current_chunk = sentence
    
    # Добавляем последний чанк
    if current_chunk:
        chunks.append(current_chunk + ".")
    
    return chunks

def translate_chunk(translator, text, retry_count=3):
    """Переводит один фрагмент текста с повторными попытками"""
    for attempt in range(retry_count):
        try:
            result = translator.translate(text, src='en', dest='ru')
            return result.text
        except Exception as e:
            print(f"    Ошибка при переводе (попытка {attempt + 1}): {e}")
            if attempt < retry_count - 1:
                time.sleep(2)  # Пауза перед повторной попыткой
            else:
                print(f"    Не удалось перевести фрагмент, оставляем оригинал")
                return text

def translate_file_in_parts(input_file, output_file):
    """Переводит файл по частям"""
    print(f"Читаем файл: {input_file}")
    
    # Читаем файл
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print(f"Размер файла: {len(content)} символов")
    
    # Очищаем текст
    print("Очищаем текст...")
    cleaned_text = clean_text(content)
    print(f"После очистки: {len(cleaned_text)} символов")
    
    # Разбиваем на части
    print("Разбиваем на части...")
    chunks = split_into_chunks(cleaned_text, max_length=800)
    print(f"Получено {len(chunks)} частей для перевода")
    
    # Показываем первые части
    print("\nПервые 3 части:")
    for i, chunk in enumerate(chunks[:3]):
        print(f"{i+1}. {chunk[:100]}...")
    
    # Переводим по частям
    translator = Translator()
    translated_chunks = []
    
    print(f"\nНачинаем перевод {len(chunks)} частей...")
    
    for i, chunk in enumerate(chunks):
        print(f"Переводим часть {i+1}/{len(chunks)}...")
        translated = translate_chunk(translator, chunk)
        translated_chunks.append(translated)
        
        # Пауза между запросами
        time.sleep(1)
        
        # Показываем прогресс каждые 10 частей
        if (i + 1) % 10 == 0:
            print(f"  Переведено {i+1} из {len(chunks)} частей")
    
    # Объединяем переведенные части
    final_text = "\n\n".join(translated_chunks)
    
    # Сохраняем результат
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(final_text)
    
    print(f"\n✓ Перевод завершен!")
    print(f"Результат сохранен в: {output_file}")
    print(f"Размер переведенного текста: {len(final_text)} символов")
    
    # Показываем начало результата
    print(f"\nНачало переведенного текста:")
    print("-" * 60)
    print(final_text[:500] + "...")
    print("-" * 60)

if __name__ == "__main__":
    input_file = "subtitles_0Uu_VJeVVfo.en.txt"
    output_file = "subtitles_0Uu_VJeVVfo.ru.txt"
    
    if not os.path.exists(input_file):
        print(f"Ошибка: файл {input_file} не найден!")
        exit(1)
    
    print("Простой переводчик субтитров")
    print("=" * 40)
    
    try:
        translate_file_in_parts(input_file, output_file)
    except KeyboardInterrupt:
        print("\nПеревод прерван пользователем")
    except Exception as e:
        print(f"Ошибка: {e}")
